<view class="orders-container">
  <!-- 列表视图 -->
  <view wx:if="{{!showDetail}}">
    <!-- 快捷操作区域 -->
    <view class="quick-actions">
      <view class="action-item" bindtap="goToPendingRepairs">
        <van-icon name="todo-list-o" size="20px" color="#1989fa" />
        <text class="action-text">待处理报修</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>
    </view>

  <!-- Tab切换 -->
  <van-tabs active="{{activeTab}}" bind:change="onTabChange" sticky>
    <van-tab wx:for="{{tabs}}" wx:key="key" title="{{item.title}}">
      <!-- 报修列表 -->
      <view wx:if="{{item.key === 'repair'}}" class="tab-content">
        <view wx:if="{{loading && repairList.length === 0}}" class="loading">
          <van-loading type="spinner" size="24px">加载中...</van-loading>
        </view>

        <view wx:else class="order-list">
          <view wx:for="{{repairList}}" wx:key="id" class="order-item" bindtap="viewDetail" data-id="{{item.id}}">
            <view class="item-header">
              <view class="item-type">{{item.type || '报修'}}</view>
              <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="item-content">{{item.contentSummary}}</view>
            <view class="item-info">
              <view class="info-left">
                <text class="info-user">{{item.name}}</text>
                <text class="info-phone">{{item.phone}}</text>
              </view>
              <view class="info-right">
                <text class="info-time">{{item.create_time}}</text>
              </view>
            </view>
            <view wx:if="{{item.address}}" class="item-address">
              <van-icon name="location-o" size="12px" />
              <text>{{item.address}}</text>
            </view>
          </view>

          <view wx:if="{{repairList.length === 0 && !loading}}" class="empty">
            <van-empty description="暂无报修单" />
          </view>

          <view wx:if="{{loading && repairList.length > 0}}" class="loading-more">
            <van-loading type="spinner" size="16px">加载更多...</van-loading>
          </view>

          <view wx:if="{{!hasMore && repairList.length > 0}}" class="no-more">
            <text>没有更多数据了</text>
          </view>
        </view>
      </view>

      <!-- 投诉建议列表 -->
      <view wx:if="{{item.key === 'complaint'}}" class="tab-content">
        <view wx:if="{{loading && complaintList.length === 0}}" class="loading">
          <van-loading type="spinner" size="24px">加载中...</van-loading>
        </view>

        <view wx:else class="order-list">
          <view wx:for="{{complaintList}}" wx:key="id" class="order-item" bindtap="viewDetail" data-id="{{item.id}}">
            <view class="item-header">
              <view class="item-type">{{item.type || '投诉建议'}}</view>
              <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="item-content">{{item.contentSummary}}</view>
            <view class="item-info">
              <view class="info-left">
                <text class="info-user">{{item.name}}</text>
                <text class="info-phone">{{item.phone}}</text>
              </view>
              <view class="info-right">
                <text class="info-time">{{item.create_time}}</text>
              </view>
            </view>
            <view wx:if="{{item.address}}" class="item-address">
              <van-icon name="location-o" size="12px" />
              <text>{{item.address}}</text>
            </view>
          </view>

          <view wx:if="{{complaintList.length === 0 && !loading}}" class="empty">
            <van-empty description="暂无投诉建议" />
          </view>

          <view wx:if="{{loading && complaintList.length > 0}}" class="loading-more">
            <van-loading type="spinner" size="16px">加载更多...</van-loading>
          </view>

          <view wx:if="{{!hasMore && complaintList.length > 0}}" class="no-more">
            <text>没有更多数据了</text>
          </view>
        </view>
      </view>
    </van-tab>
  </van-tabs>
  </view>

  <!-- 详情视图 -->
  <view wx:if="{{showDetail}}" class="detail-container">
    <!-- 返回按钮 -->
    <view class="detail-header">
      <van-icon name="arrow-left" size="20px" bindtap="backToList" />
      <text class="detail-title">{{currentType === 'repair' ? '报修详情' : '投诉建议详情'}}</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <van-loading type="spinner" size="24px">加载中...</van-loading>
    </view>

    <!-- 详情内容 -->
    <view wx:else class="detail-content">
      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-left">
            <text class="type-text">{{detail.type || (currentType === 'repair' ? '报修' : '投诉建议')}}</text>
            <view class="status-tag" style="background-color: {{getStatusColor(detail.status)}};">
              {{getStatusText(detail.status)}}
            </view>
          </view>
          <view class="header-right">
            <text class="time-text">{{detail.create_time}}</text>
          </view>
        </view>

        <view class="card-content">
          <view class="content-text">{{detail.content}}</view>

          <view wx:if="{{detail.address}}" class="address-info">
            <van-icon name="location-o" size="14px" color="#999" />
            <text class="address-text">{{detail.address}}</text>
          </view>
        </view>
      </view>

      <!-- 联系人信息 -->
      <view class="contact-card">
        <view class="card-title">联系人信息</view>
        <view class="contact-info">
          <view class="contact-item">
            <text class="contact-label">姓名：</text>
            <text class="contact-value">{{detail.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话：</text>
            <text class="contact-value phone-link" bindtap="makeCall" data-phone="{{detail.phone}}">{{detail.phone}}</text>
          </view>
        </view>
      </view>

      <!-- 附件信息 -->
      <view wx:if="{{attachments.length > 0}}" class="attachment-card">
        <view class="card-title">相关附件</view>
        <view class="attachment-list">
          <view wx:for="{{attachments}}" wx:key="id" class="attachment-item">
            <image
              class="attachment-image"
              src="{{item.url}}"
              mode="aspectFill"
              bindtap="previewImage"
              data-url="{{item.url}}"
              data-index="{{index}}"
            />
          </view>
        </view>
      </view>

      <!-- 处理信息 -->
      <view wx:if="{{detail.handler || detail.feedback}}" class="process-card">
        <view class="card-title">处理信息</view>
        <view class="process-info">
          <view wx:if="{{detail.handler}}" class="process-item">
            <text class="process-label">处理人：</text>
            <text class="process-value">{{detail.handler}}</text>
          </view>
          <view wx:if="{{detail.handling_time}}" class="process-item">
            <text class="process-label">处理时间：</text>
            <text class="process-value">{{detail.handling_time}}</text>
          </view>
          <view wx:if="{{detail.feedback}}" class="process-item">
            <text class="process-label">处理反馈：</text>
            <text class="process-value feedback-text">{{detail.feedback}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <van-button
          type="primary"
          size="large"
          bindtap="showStatusUpdate"
          loading="{{processing}}"
        >
          更新状态
        </van-button>

        <van-button
          type="default"
          size="large"
          bindtap="showFeedbackInput"
          custom-class="feedback-btn"
        >
          添加反馈
        </van-button>
      </view>
    </view>

    <!-- 状态更新对话框 -->
    <van-dialog
      show="{{showStatusDialog}}"
      title="更新状态"
      show-cancel-button
      bind:confirm="confirmStatusUpdate"
      bind:cancel="closeDialog"
      confirm-button-loading="{{processing}}"
    >
      <view class="dialog-content">
        <van-radio-group value="{{selectedStatus}}" bind:change="onStatusChange">
          <van-radio wx:for="{{statusOptions}}" wx:key="value" name="{{item.value}}">
            {{item.label}}
          </van-radio>
        </van-radio-group>
      </view>
    </van-dialog>

    <!-- 反馈输入对话框 -->
    <van-dialog
      show="{{showFeedbackDialog}}"
      title="添加处理反馈"
      show-cancel-button
      bind:confirm="confirmStatusUpdate"
      bind:cancel="closeDialog"
      confirm-button-loading="{{processing}}"
    >
      <view class="dialog-content">
        <van-field
          value="{{feedback}}"
          type="textarea"
          placeholder="请输入处理反馈..."
          autosize
          maxlength="500"
          show-word-limit
          bind:change="onFeedbackInput"
        />
      </view>
    </van-dialog>
  </view>
</view>
